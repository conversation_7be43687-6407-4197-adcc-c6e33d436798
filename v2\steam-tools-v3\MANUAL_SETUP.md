# Manual Setup Guide - Steam Tools v3

If the automated build script fails, follow these manual steps:

## 🔧 Fix npm Issue

### Step 1: Check Environment
```bash
cd build
check-env.bat
```

### Step 2: Fix npm (Most Common Issue)

**Option A: Reinstall Node.js (Recommended)**
1. Go to https://nodejs.org/
2. Download the LTS version
3. **Important**: During installation, check "Add to PATH"
4. Restart your command prompt/PowerShell
5. Test: `npm --version`

**Option B: Fix PATH manually**
1. Find your Node.js installation (usually `C:\Program Files\nodejs\`)
2. Add it to your PATH environment variable
3. Restart command prompt
4. Test: `npm --version`

**Option C: Use full path**
If npm is at `C:\Program Files\nodejs\npm.cmd`, you can use that directly.

## 🛠️ Manual Build Steps

### 1. Backend Setup
```bash
cd backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

Test backend:
```bash
python main.py
# Should start server on http://localhost:8000
# Press Ctrl+C to stop
```

### 2. Frontend Setup
```bash
cd frontend
npm install
# or: C:\Program Files\nodejs\npm.cmd install
```

Test frontend:
```bash
npm run build
# Should create build/ folder
```

### 3. Electron Setup
```bash
cd electron
npm install
# or: C:\Program Files\nodejs\npm.cmd install
```

### 4. Development Mode Test
```bash
# Terminal 1 - Backend
cd backend
python main.py

# Terminal 2 - Frontend (new terminal)
cd frontend
npm start

# Terminal 3 - Electron (new terminal)
cd electron
npm run dev
```

### 5. Production Build
```bash
# Make sure frontend is built first
cd frontend
npm run build

# Then build Electron
cd ../electron
npm run build-win
```

## 🐛 Common Issues & Solutions

### "npm not found"
- **Cause**: Node.js not in PATH or not installed properly
- **Fix**: Reinstall Node.js with PATH option checked

### "python not found"
- **Cause**: Python not in PATH
- **Fix**: Add Python to PATH or reinstall with PATH option

### "Module not found" errors
- **Cause**: Dependencies not installed
- **Fix**: Run `pip install -r requirements.txt` and `npm install`

### "Port already in use"
- **Cause**: Another process using port 8000 or 3000
- **Fix**: Kill the process or change ports in settings

### "Permission denied"
- **Cause**: Need admin rights for some operations
- **Fix**: Run command prompt as Administrator

### Backend won't start
```bash
cd backend
pip install -r requirements.txt
python -c "import fastapi; print('FastAPI OK')"
python -c "import uvicorn; print('Uvicorn OK')"
python main.py
```

### Frontend won't build
```bash
cd frontend
npm install
npm run build
```

### Electron won't package
```bash
cd electron
npm install
npm list electron-builder
npm run build-win
```

## 📁 Expected Output

After successful build:
```
dist/
├── Steam Tools v3 Setup.exe    # Main installer
├── win-unpacked/               # Unpacked app files
└── build_info.json            # Build information
```

## 🔍 Verification Steps

1. **Backend Test**: Visit http://localhost:8000/health
2. **Frontend Test**: Check `frontend/build/` folder exists
3. **Electron Test**: Run the .exe file from `dist/`

## 📞 Still Having Issues?

1. Run `check-env.bat` and fix any ❌ items
2. Try each step manually and note where it fails
3. Check the error messages carefully
4. Make sure all prerequisites are properly installed

## 🎯 Quick Test Commands

```bash
# Test all tools are available
python --version
node --version
npm --version

# Test Python modules
python -c "import fastapi, uvicorn, requests; print('Backend deps OK')"

# Test npm in different ways
npm --version
npm.cmd --version
"C:\Program Files\nodejs\npm.cmd" --version
```
