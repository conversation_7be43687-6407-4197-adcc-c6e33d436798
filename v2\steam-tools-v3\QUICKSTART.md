# Steam Tools v3 - Quick Start Guide

## 🚀 Quick Start

### Prerequisites
- **Python 3.8+** - [Download Python](https://python.org/downloads/)
- **Node.js 18+** - [Download Node.js](https://nodejs.org/)
- **Windows 10/11** (for Steam integration)

### Development Mode (Fastest)

1. **Clone/Extract** the project to your desired location
2. **Run Development Mode**:
   ```bash
   # Windows
   cd build
   dev.bat
   
   # Or manually:
   # Terminal 1: cd backend && python main.py
   # Terminal 2: cd frontend && npm start  
   # Terminal 3: cd electron && npm run dev
   ```

This will start:
- Python backend on `http://localhost:8000`
- React frontend on `http://localhost:3000`
- Electron desktop app

### Production Build

1. **Build Everything**:
   ```bash
   # Windows
   cd build
   build.bat
   
   # Or manually:
   python build.py
   ```

2. **Find Your Executable**:
   - Output: `dist/Steam Tools v3 Setup.exe`
   - Size: ~80-120 MB (includes everything)

### Manual Setup (If Scripts Fail)

#### Backend Setup
```bash
cd backend
python -m venv venv
venv\Scripts\activate  # Windows
pip install -r requirements.txt
python main.py
```

#### Frontend Setup
```bash
cd frontend
npm install
npm start  # Development
npm run build  # Production
```

#### Electron Setup
```bash
cd electron
npm install
npm run dev  # Development
npm run build  # Production
```

## 🔧 Configuration

### Backend Configuration
Edit `backend/config/settings.py`:
- KeyAuth credentials
- Server settings
- Admin password hash

### Frontend Configuration
Edit `frontend/src/config/`:
- API endpoints
- UI settings

## 🐛 Troubleshooting

### Common Issues

**"Python not found"**
- Install Python 3.8+ and add to PATH
- Restart terminal after installation

**"Node.js not found"**
- Install Node.js 18+ and add to PATH
- Restart terminal after installation

**"Backend won't start"**
- Check Python dependencies: `pip install -r requirements.txt`
- Check port 8000 is not in use

**"Frontend won't start"**
- Check Node dependencies: `npm install`
- Check port 3000 is not in use

**"Electron won't start"**
- Ensure backend and frontend are running first
- Check Electron dependencies: `npm install`

### Port Configuration
- Backend: `localhost:8000` (configurable in settings.py)
- Frontend: `localhost:3000` (React dev server)
- WebSocket: `ws://localhost:8000/ws/` (for real-time updates)

## 📁 Project Structure

```
steam-tools-v3/
├── backend/           # Python FastAPI backend
├── frontend/          # React TypeScript frontend  
├── electron/          # Electron desktop wrapper
├── build/             # Build scripts and tools
└── dist/              # Output directory
```

## 🎯 Features

✅ **Modern React UI** - Steam-inspired design with animations
✅ **Python Backend** - All original functionality preserved
✅ **Real-time Updates** - WebSocket progress tracking
✅ **Desktop App** - Single executable via Electron
✅ **Type Safety** - Full TypeScript implementation
✅ **Security** - Proper Electron security practices

## 📞 Support

If you encounter issues:
1. Check this guide first
2. Verify all prerequisites are installed
3. Try the manual setup steps
4. Check the console for error messages

## 🔄 Development Workflow

1. **Make Changes** to backend/frontend code
2. **Test in Development Mode** (`dev.bat`)
3. **Build for Production** (`build.bat`)
4. **Test the Executable** in `dist/` folder

The development mode supports hot-reload for both frontend and backend changes.
