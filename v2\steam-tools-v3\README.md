# Steam Tools v3 - Electron + React + Python Architecture

Modern Steam activation tool with React frontend and Python backend.

## 🏗️ Architecture

```
steam-tools-v3/
├── backend/                 # Python FastAPI backend
│   ├── main.py             # FastAPI server entry point
│   ├── models/             # Pydantic data models
│   ├── services/           # Business logic services
│   ├── utils/              # Utility functions
│   ├── config/             # Configuration management
│   └── requirements.txt    # Python dependencies
├── frontend/               # React TypeScript frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API service layer
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Frontend utilities
│   │   └── styles/         # Tailwind CSS styles
│   ├── public/             # Static assets
│   └── package.json        # Node.js dependencies
├── electron/               # Electron desktop wrapper
│   ├── main.js             # Electron main process
│   ├── preload.js          # Security bridge
│   └── package.json        # Electron configuration
├── build/                  # Build scripts and configuration
│   ├── build.py            # Main build script
│   ├── electron-builder.yml # Electron Builder config
│   └── requirements.txt    # Build dependencies
└── dist/                   # Distribution files
```

## 🚀 Features

- **Modern React UI** - Steam-inspired design with smooth animations
- **Python Backend** - All existing functionality preserved
- **Desktop App** - Single executable file via Electron
- **Real-time Updates** - WebSocket communication for progress
- **Type Safety** - Full TypeScript implementation
- **Security** - Proper Electron security practices

## 🛠️ Development

### Prerequisites
- Python 3.8+
- Node.js 18+
- npm or yarn

### Setup
```bash
# Install Python dependencies
cd backend
pip install -r requirements.txt

# Install frontend dependencies
cd ../frontend
npm install

# Install Electron dependencies
cd ../electron
npm install
```

### Development Mode
```bash
# Start Python backend (Terminal 1)
cd backend
python main.py

# Start React frontend (Terminal 2)
cd frontend
npm start

# Start Electron app (Terminal 3)
cd electron
npm run dev
```

### Build for Production
```bash
# Build everything
python build/build.py

# Output: dist/SteamTools-v3-Setup.exe
```

## 📦 Distribution

The build process creates a single executable installer that includes:
- Electron app with React frontend
- Embedded Python backend
- All dependencies bundled
- Windows installer with auto-updater

## 🔧 Configuration

Configuration is managed through:
- `backend/config/settings.py` - Backend settings
- `frontend/src/config/` - Frontend configuration
- `electron/config.json` - Electron app settings

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test

# E2E tests
npm run test:e2e
```
