"""Application settings and configuration."""

import os
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Server settings
    host: str = "127.0.0.1"
    port: int = 8000
    debug: bool = False
    
    # KeyAuth settings
    keyauth_name: str = "MainSteam"
    keyauth_owner_id: str = "1tGVnUKtzH"
    keyauth_secret: str = "eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f"
    keyauth_version: str = "1.0"
    
    # File paths
    config_file: str = "config.json"
    log_file: str = "steam_tools.log"
    
    # Security settings
    admin_password_hash: str = "240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9"  # admin123
    
    # Steam settings
    steam_registry_path: str = r"SOFTWARE\WOW6432Node\Valve\Steam"
    steam_registry_key: str = "InstallPath"
    
    # Download settings
    download_timeout: int = 30
    max_retries: int = 3
    chunk_size: int = 8192
    
    # Logging settings
    log_level: str = "INFO"
    max_log_entries: int = 10000
    
    # CORS settings
    cors_origins: list = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()
