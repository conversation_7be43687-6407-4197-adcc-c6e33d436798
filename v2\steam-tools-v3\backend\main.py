"""FastAPI backend for Steam Tools v3."""

import asyncio
import json
import logging
import os
from contextlib import asynccontextmanager
from typing import Dict, Any

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from config.settings import settings
from models.steam import (
    LicenseKeyRequest, LicenseValidationResponse, SteamPathRequest,
    SteamPathResponse, DownloadRequest, SystemInfo, StatusResponse,
    AdminRequest, AdminResponse
)
from services.keyauth_service import KeyAuthService
from services.steam_service import SteamService
from services.download_service import DownloadService
from services.config_service import ConfigService
from services.admin_service import AdminService

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global service instances
keyauth_service: KeyAuthService = None
steam_service: SteamService = None
download_service: DownloadService = None
config_service: ConfigService = None
admin_service: AdminService = None

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket client {client_id} connected")

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"WebSocket client {client_id} disconnected")

    async def send_message(self, client_id: str, message: dict):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)

    async def broadcast(self, message: dict):
        disconnected = []
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected.append(client_id)
        
        for client_id in disconnected:
            self.disconnect(client_id)

manager = ConnectionManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    global keyauth_service, steam_service, download_service, config_service, admin_service
    
    logger.info("Starting Steam Tools v3 Backend")
    
    # Initialize services
    keyauth_service = KeyAuthService()
    steam_service = SteamService()
    download_service = DownloadService(steam_service, manager)
    config_service = ConfigService()
    admin_service = AdminService(config_service)
    
    logger.info("All services initialized successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Steam Tools v3 Backend")

# Create FastAPI app
app = FastAPI(
    title="Steam Tools v3 API",
    description="Backend API for Steam activation tool",
    version="3.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "3.0.0"}

# System information endpoint
@app.get("/api/system/info", response_model=SystemInfo)
async def get_system_info():
    """Get system information."""
    return await steam_service.get_system_info()

# License validation endpoint
@app.post("/api/license/validate", response_model=LicenseValidationResponse)
async def validate_license(request: LicenseKeyRequest):
    """Validate license key."""
    try:
        response = await keyauth_service.validate_license(request.license_key)
        
        # Save to history if valid
        if response.is_valid and response.app_info:
            await config_service.add_license_history(
                request.license_key, 
                response.app_info, 
                True
            )
        
        return response
    except Exception as e:
        logger.error(f"Error validating license: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Steam path detection endpoint
@app.post("/api/steam/detect-path", response_model=SteamPathResponse)
async def detect_steam_path(request: SteamPathRequest):
    """Detect Steam installation path."""
    try:
        response = await steam_service.detect_steam_path(request.custom_path)
        
        # Save to config if valid
        if response.is_valid and response.steam_path:
            await config_service.set_steam_path(response.steam_path)
        
        return response
    except Exception as e:
        logger.error(f"Error detecting Steam path: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Steam process management
@app.post("/api/steam/close-processes")
async def close_steam_processes():
    """Close Steam processes."""
    try:
        success, message = await steam_service.close_steam_processes()
        return StatusResponse(success=success, message=message)
    except Exception as e:
        logger.error(f"Error closing Steam processes: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Download files endpoint
@app.post("/api/download/start")
async def start_download(request: DownloadRequest, background_tasks: BackgroundTasks):
    """Start file download process."""
    try:
        # Validate request
        if not steam_service.steam_path:
            await steam_service.detect_steam_path()
        
        if not steam_service.steam_path:
            raise HTTPException(status_code=400, detail="Steam path not found")
        
        # Start download in background
        background_tasks.add_task(
            download_service.download_files,
            request.license_key,
            request.app_info,
            request.steam_path
        )
        
        return StatusResponse(success=True, message="Download started")
        
    except Exception as e:
        logger.error(f"Error starting download: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Admin authentication
@app.post("/api/admin/authenticate", response_model=AdminResponse)
async def authenticate_admin(request: AdminRequest):
    """Authenticate admin user."""
    try:
        is_valid = await admin_service.verify_password(request.password)
        
        if is_valid:
            return AdminResponse(authenticated=True)
        else:
            return AdminResponse(
                authenticated=False, 
                error_message="Invalid password"
            )
    except Exception as e:
        logger.error(f"Error authenticating admin: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Configuration endpoints
@app.get("/api/config")
async def get_config():
    """Get application configuration."""
    try:
        config = await config_service.get_config()
        return {"success": True, "data": config}
    except Exception as e:
        logger.error(f"Error getting config: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/api/config/reset")
async def reset_config():
    """Reset application configuration."""
    try:
        await config_service.reset_config()
        return StatusResponse(success=True, message="Configuration reset")
    except Exception as e:
        logger.error(f"Error resetting config: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# WebSocket endpoint for real-time updates
@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for real-time communication."""
    await manager.connect(websocket, client_id)
    try:
        while True:
            # Keep connection alive
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle ping/pong for connection health
            if message.get("type") == "ping":
                await manager.send_message(client_id, {"type": "pong"})
                
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        manager.disconnect(client_id)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
