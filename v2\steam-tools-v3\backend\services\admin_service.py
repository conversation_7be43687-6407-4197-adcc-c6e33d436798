"""Admin service for authentication and administrative functions."""

import hashlib
import logging
from datetime import datetime
from typing import Dict, Any, List

from services.config_service import ConfigService

logger = logging.getLogger(__name__)


class AdminService:
    """Service for admin authentication and functions."""
    
    def __init__(self, config_service: ConfigService):
        self.config_service = config_service
    
    def _hash_password(self, password: str) -> str:
        """Hash password using SHA256."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    async def verify_password(self, password: str) -> bool:
        """Verify admin password."""
        try:
            password_hash = self._hash_password(password)
            admin_settings = await self.config_service.get_admin_settings()
            stored_hash = admin_settings.get("password_hash", "")
            
            is_valid = password_hash == stored_hash
            
            if is_valid:
                logger.info("Admin authentication successful")
            else:
                logger.warning("Admin authentication failed")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Error verifying admin password: {e}")
            return False
    
    async def change_password(self, new_password: str) -> bool:
        """Change admin password."""
        try:
            new_hash = self._hash_password(new_password)
            
            await self.config_service.update_admin_settings({
                "password_hash": new_hash
            })
            
            logger.info("Admin password changed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error changing admin password: {e}")
            return False
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get system statistics for admin dashboard."""
        try:
            config = await self.config_service.get_config()
            license_history = await self.config_service.get_license_history()
            
            # Calculate statistics
            total_activations = len(license_history)
            successful_activations = len([h for h in license_history if h.get("success", False)])
            failed_activations = total_activations - successful_activations
            
            # Get unique apps
            unique_apps = set()
            for entry in license_history:
                if entry.get("app_name"):
                    unique_apps.add(entry["app_name"])
            
            # Recent activity (last 10 entries)
            recent_activity = license_history[:10]
            
            return {
                "total_activations": total_activations,
                "successful_activations": successful_activations,
                "failed_activations": failed_activations,
                "success_rate": (successful_activations / total_activations * 100) if total_activations > 0 else 0,
                "unique_apps": len(unique_apps),
                "recent_activity": recent_activity,
                "steam_path_configured": bool(config.get("steam_path")),
                "cache_entries": len(config.get("app_cache", {}))
            }
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}
    
    async def get_detailed_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get detailed application logs for admin review."""
        try:
            # In a real implementation, this would read from log files
            # For now, return license history as a form of logs
            license_history = await self.config_service.get_license_history()
            
            # Convert to log format
            logs = []
            for entry in license_history[:limit]:
                logs.append({
                    "timestamp": entry.get("timestamp"),
                    "level": "INFO" if entry.get("success") else "ERROR",
                    "category": "LICENSE",
                    "message": f"License activation for {entry.get('app_name', 'Unknown')}",
                    "details": {
                        "license_key": entry.get("key", "")[:10] + "...",
                        "app_id": entry.get("app_id"),
                        "success": entry.get("success")
                    }
                })
            
            return logs
            
        except Exception as e:
            logger.error(f"Error getting detailed logs: {e}")
            return []
    
    async def cleanup_old_data(self, days: int = 30) -> Dict[str, int]:
        """Clean up old data (cache, logs, etc.)."""
        try:
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Clean up license history
            license_history = await self.config_service.get_license_history()
            original_count = len(license_history)
            
            cleaned_history = []
            for entry in license_history:
                try:
                    entry_date = datetime.fromisoformat(entry.get("timestamp", ""))
                    if entry_date > cutoff_date:
                        cleaned_history.append(entry)
                except Exception:
                    # Keep entries with invalid timestamps
                    cleaned_history.append(entry)
            
            # Update config with cleaned history
            config = await self.config_service.get_config()
            config["license_key_history"] = cleaned_history
            
            # Clean up app cache
            app_cache = config.get("app_cache", {})
            original_cache_count = len(app_cache)
            
            cleaned_cache = {}
            for app_id, cache_data in app_cache.items():
                try:
                    cached_date = datetime.fromisoformat(cache_data.get("cached_at", ""))
                    if cached_date > cutoff_date:
                        cleaned_cache[app_id] = cache_data
                except Exception:
                    # Keep cache entries with invalid timestamps
                    cleaned_cache[app_id] = cache_data
            
            config["app_cache"] = cleaned_cache
            
            # Save cleaned config
            await self.config_service.config_data.model_validate(config)
            await self.config_service.save_config()
            
            cleaned_stats = {
                "history_entries_removed": original_count - len(cleaned_history),
                "cache_entries_removed": original_cache_count - len(cleaned_cache),
                "days_threshold": days
            }
            
            logger.info(f"Cleanup completed: {cleaned_stats}")
            return cleaned_stats
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return {"error": str(e)}
    
    async def export_admin_data(self) -> Dict[str, Any]:
        """Export all admin data for backup."""
        try:
            config = await self.config_service.get_config()
            stats = await self.get_system_stats()
            logs = await self.get_detailed_logs(1000)  # Get more logs for export
            
            return {
                "export_timestamp": datetime.now().isoformat(),
                "configuration": config,
                "statistics": stats,
                "logs": logs,
                "version": "3.0.0"
            }
            
        except Exception as e:
            logger.error(f"Error exporting admin data: {e}")
            return {"error": str(e)}
