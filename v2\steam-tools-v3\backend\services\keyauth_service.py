"""KeyAuth service for license validation and app data retrieval."""

import json
import hashlib
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from utils.keyauth import api
from models.steam import AppInfo, LicenseValidationResponse
from config.settings import settings

logger = logging.getLogger(__name__)


class KeyAuthService:
    """Service for handling KeyAuth API operations."""
    
    def __init__(self):
        self.keyauth_app: Optional[api] = None
        self._initialize_keyauth()
    
    def _initialize_keyauth(self) -> None:
        """Initialize KeyAuth API connection."""
        try:
            checksum = self._get_file_checksum()
            
            logger.info("Initializing KeyAuth API", extra={
                "app_name": settings.keyauth_name,
                "owner_id": settings.keyauth_owner_id,
                "version": settings.keyauth_version,
                "checksum": checksum
            })
            
            self.keyauth_app = api(
                name=settings.keyauth_name,
                ownerid=settings.keyauth_owner_id,
                secret=settings.keyauth_secret,
                version=settings.keyauth_version,
                hash_to_check=checksum
            )
            
            logger.info("KeyAuth API initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize KeyAuth API: {str(e)}")
            self.keyauth_app = None
    
    def _get_file_checksum(self) -> str:
        """Get file checksum for KeyAuth validation."""
        try:
            # In production, this would be the main executable
            # For development, we'll use a placeholder
            return "development_checksum"
        except Exception:
            return ""
    
    async def validate_license(self, license_key: str) -> LicenseValidationResponse:
        """Validate license key and return app information."""
        try:
            if not self.keyauth_app:
                return LicenseValidationResponse(
                    is_valid=False,
                    error_message="KeyAuth not initialized"
                )
            
            logger.info(f"Validating license key: {license_key[:10]}...")
            
            # Validate license with KeyAuth
            is_valid = self.keyauth_app.license(license_key)
            
            if not is_valid:
                return LicenseValidationResponse(
                    is_valid=False,
                    error_message="Invalid license key"
                )
            
            # Get app data from KeyAuth variable
            main_data = self.keyauth_app.var("Main")
            
            if not main_data:
                return LicenseValidationResponse(
                    is_valid=False,
                    error_message="Failed to retrieve app data"
                )
            
            # Parse app data
            try:
                apps_data = json.loads(main_data)
            except json.JSONDecodeError:
                return LicenseValidationResponse(
                    is_valid=False,
                    error_message="Invalid app data format"
                )
            
            # Find matching app
            app_info = self._find_matching_app(license_key, apps_data.get("apps", []))
            
            if not app_info:
                return LicenseValidationResponse(
                    is_valid=False,
                    error_message="No matching app found for license key"
                )
            
            logger.info(f"License validated successfully for app: {app_info.app_name}")
            
            return LicenseValidationResponse(
                is_valid=True,
                app_info=app_info
            )
            
        except Exception as e:
            logger.error(f"Error validating license: {str(e)}")
            return LicenseValidationResponse(
                is_valid=False,
                error_message="An error occurred during validation"
            )
    
    def _find_matching_app(self, license_key: str, apps: list) -> Optional[AppInfo]:
        """Find app that matches the license key prefix."""
        for app in apps:
            if license_key.startswith(app.get("license_prefix", "")):
                # Get download URLs from KeyAuth variables
                download_urls = self._get_download_urls()
                
                return AppInfo(
                    app_id=app.get("app_id", ""),
                    app_name=app.get("app_name", ""),
                    license_prefix=app.get("license_prefix", ""),
                    download_urls=download_urls
                )
        return None
    
    def _get_download_urls(self) -> Dict[str, str]:
        """Get download URLs from KeyAuth variables."""
        urls = {}
        
        try:
            # Get URLs from KeyAuth variables
            url_mappings = {
                "hid_dll": "SteamTools",
                "luapacka": "luapacka", 
                "steamtools": "steamtools",
                "lua_script": "luascript"
            }
            
            for key, var_name in url_mappings.items():
                try:
                    url = self.keyauth_app.var(var_name)
                    if url:
                        urls[key] = url
                except Exception as e:
                    logger.warning(f"Failed to get {var_name} URL: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error getting download URLs: {str(e)}")
        
        return urls
    
    def is_initialized(self) -> bool:
        """Check if KeyAuth is properly initialized."""
        return self.keyauth_app is not None and self.keyauth_app.initialized
