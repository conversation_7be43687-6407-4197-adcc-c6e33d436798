"""KeyAuth API integration - copied from original src/keyauth.py"""

import binascii  # hex encoding
import json as jsond  # json
import os
import platform  # check platform
import subprocess  # needed for mac device
import time  # sleep before exit
from uuid import uuid4  # gen random guid

class colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    RESET = '\033[0m'

try:
    if os.name == 'nt':
        import win32security  # get sid (WIN only)
    import requests  # https requests
    from Crypto.Cipher import AES
    from Crypto.Hash import SHA256
    from Crypto.Util.Padding import pad, unpad
except ModuleNotFoundError:
    print("Exception when importing modules")
    print("Installing necessary modules....")
    if os.path.isfile("../config/requirements.txt"):
        os.system("pip install -r ../config/requirements.txt")
    else:
        os.system("pip install pywin32")
        os.system("pip install pycryptodome")
        os.system("pip install requests")
    print("Modules installed!")
    time.sleep(1.5)
    os._exit(1)

try:  # Connection check
    s = requests.Session()  # Session
    s.get('https://google.com')
except requests.exceptions.RequestException as e:
    print(e)
    time.sleep(5)
    os._exit(1)


class api:
    name = ownerid = secret = version = hash_to_check = ""

    def __init__(self, name, ownerid, secret, version, hash_to_check):
        if len(ownerid) != 10 and len(secret) != 64:
            print("Application not setup correctly. Please watch video link found in README.md")
            time.sleep(3)
            os._exit(1)
        self.name = name
        self.ownerid = ownerid
        self.secret = secret
        self.version = version
        self.hash_to_check = hash_to_check
        self.init()

    sessionid = enckey = ""
    initialized = False

    def init(self):
        if self.sessionid != "":
            print("You've already initialized!")
            time.sleep(3)
            os._exit(1)
        sent_key = str(uuid4())[:16]
        self.enckey = sent_key + "-" + self.secret
        post_data = {
            "type": binascii.hexlify(("init").encode()).decode(),
            "ver": encryption.encrypt(self.version, sent_key, self.secret),
            "hash": encryption.encrypt(self.hash_to_check, sent_key, self.secret),
            "enckey": encryption.encrypt(sent_key, self.secret, self.secret),
            "name": binascii.hexlify(self.name.encode()).decode(),
            "ownerid": binascii.hexlify(self.ownerid.encode()).decode()
        }
        response = self.__do_request(post_data)
        if response == "KeyAuth_Invalid":
            print("Application not found")
            time.sleep(3)
            os._exit(1)
        response = encryption.decrypt(response, sent_key, self.secret)
        json = jsond.loads(response)
        if json["message"] == "invalidver":
            if json["download"] != "":
                print("New Version Available")
                download_link = json["download"]
                os.system(f"start {download_link}")
                time.sleep(3)
                os._exit(1)
            else:
                print("Invalid Version, Contact owner to add download link to latest app version")
                time.sleep(3)
                os._exit(1)
        if not json["success"]:
            print(json["message"])
            time.sleep(3)
            os._exit(1)
        self.sessionid = json["sessionid"]
        self.initialized = True

    def license(self, key):
        self.checkinit()
        hwid = others.get_hwid()
        post_data = {
            "type": binascii.hexlify(("license").encode()).decode(),
            "key": encryption.encrypt(key, self.enckey, self.enckey),
            "hwid": encryption.encrypt(hwid, self.enckey, self.enckey),
            "sessionid": binascii.hexlify(self.sessionid.encode()).decode()
        }
        response = self.__do_request(post_data)
        response = encryption.decrypt(response, self.enckey, self.enckey)
        json = jsond.loads(response)
        if json["success"]:
            print("Successfully Activated")
            self.__load_user_data(json["info"])
            return True
        else:
            print(json["message"])
            return False

    def var(self, name):
        self.checkinit()
        post_data = {
            "type": binascii.hexlify(("var").encode()).decode(),
            "varid": encryption.encrypt(name, self.enckey, self.enckey),
            "sessionid": binascii.hexlify(self.sessionid.encode()).decode()
        }
        response = self.__do_request(post_data)
        response = encryption.decrypt(response, self.enckey, self.enckey)
        json = jsond.loads(response)
        if json["success"]:
            return json["message"]
        else:
            print(json["message"])
            return None

    def checkinit(self):
        if not self.initialized:
            print("Initialize first, in order to use the functions")
            time.sleep(3)
            os._exit(1)

    def __load_user_data(self, data):
        self.user_data = data

    def __do_request(self, post_data):
        try:
            rq_out = requests.post("https://keyauth.win/api/1.2/", data=post_data, timeout=30)
            return rq_out.text
        except requests.exceptions.Timeout:
            print("Request timed out. Server is probably down/slow at the moment, please try again later.")
            time.sleep(3)
            os._exit(1)


class encryption:
    @staticmethod
    def encrypt(message, enc_key, secret):
        try:
            _key = SHA256.new(enc_key[:32].encode()).digest()
            _iv = enc_key[:16].encode()
            encrypted = AES.new(_key, AES.MODE_CBC, _iv).encrypt(pad(message.encode(), 16))
            return binascii.hexlify(encrypted).decode()
        except:
            print("Invalid Application Information. Please check your application details.")
            time.sleep(3)
            os._exit(1)

    @staticmethod
    def decrypt(message, enc_key, secret):
        try:
            _key = SHA256.new(enc_key[:32].encode()).digest()
            _iv = enc_key[:16].encode()
            return unpad(AES.new(_key, AES.MODE_CBC, _iv).decrypt(binascii.unhexlify(message)), 16).decode()
        except:
            print("Invalid Application Information. Please check your application details.")
            time.sleep(3)
            os._exit(1)


class others:
    @staticmethod
    def get_hwid():
        if platform.system() == "Linux":
            with open("/etc/machine-id") as f:
                hwid = f.read().strip()
        elif platform.system() == 'Windows':
            winuser = os.getenv('USERNAME')
            sid = win32security.LookupAccountName(None, winuser)[0]
            hwid = win32security.ConvertSidToStringSid(sid)
        elif platform.system() == 'Darwin':
            output = subprocess.Popen("ioreg -l | grep IOPlatformSerialNumber", shell=True, stdout=subprocess.PIPE,
                                      stderr=subprocess.STDOUT)
            hwid = output.communicate()[0].strip()
        return hwid
