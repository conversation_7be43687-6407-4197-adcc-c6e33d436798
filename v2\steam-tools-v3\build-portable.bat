@echo off
REM Steam Tools v3 - Build Portable Executable (Root Folder)

echo ========================================
echo Steam Tools v3 - Portable Build
echo ========================================
echo.

echo Building portable executable (no installer)...
echo This will create a direct .exe file without setup.
echo.

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)

REM Build frontend
echo [1/2] Building React frontend...
cd frontend
call npm run build
if errorlevel 1 (
    echo ERROR: Frontend build failed!
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ Frontend build completed.
echo.

REM Build portable executable
echo [2/2] Building portable executable...
cd electron
call npm run build-dir
if errorlevel 1 (
    echo ERROR: Electron build failed!
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo ========================================
echo ✅ BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Your portable executable is ready:
echo 📁 Location: dist\win-unpacked\Steam Tools v3.exe
echo.
echo 🚀 How to use:
echo 1. Go to: dist\win-unpacked\
echo 2. Double-click: Steam Tools v3.exe
echo 3. No installation needed!
echo.
echo 📦 To distribute:
echo Copy the entire "win-unpacked" folder
echo.

pause
