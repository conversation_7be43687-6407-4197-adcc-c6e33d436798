@echo off
REM Steam Tools v3 - Build Portable Executable

echo ========================================
echo Steam Tools v3 - Portable Build
echo ========================================
echo.

echo Building portable executable (no installer)...
echo.

REM Build frontend first
echo Step 1: Building React frontend...
cd /d "%~dp0\..\frontend"
npm run build
if errorlevel 1 (
    echo Frontend build failed!
    pause
    exit /b 1
)
echo Frontend build completed.
echo.

REM Build portable executable
echo Step 2: Building portable executable...
cd /d "%~dp0\..\electron"
npm run build-dir
if errorlevel 1 (
    echo Portable build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD COMPLETED!
echo ========================================
echo.
echo Your portable executable is ready:
echo Location: dist\win-unpacked\Steam Tools v3.exe
echo.
echo This is a standalone executable that doesn't need installation.
echo Just copy the entire "win-unpacked" folder to run anywhere.
echo.

pause
