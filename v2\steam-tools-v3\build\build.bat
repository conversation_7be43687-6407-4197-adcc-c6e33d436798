@echo off
REM Steam Tools v3 - Windows Build Script

echo ========================================
echo Steam Tools v3 - Build Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ and try again
    pause
    exit /b 1
)

REM Run the Python build script
echo Starting build process...
python build.py %*

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    echo Check the error messages above for details.
    pause
    exit /b 1
) else (
    echo.
    echo BUILD COMPLETED SUCCESSFULLY!
    echo.
    echo The application has been built and packaged.
    echo Check the 'dist' folder for the installer.
    echo.
    pause
)

exit /b 0
