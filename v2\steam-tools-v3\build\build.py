#!/usr/bin/env python3
"""
Steam Tools v3 - Complete Build Script
Builds the entire application: Backend, Frontend, and Electron packaging
"""

import os
import sys
import subprocess
import shutil
import json
import time
from pathlib import Path

class SteamToolsBuilder:
    def __init__(self):
        self.root_dir = Path(__file__).parent.parent
        self.backend_dir = self.root_dir / "backend"
        self.frontend_dir = self.root_dir / "frontend"
        self.electron_dir = self.root_dir / "electron"
        self.build_dir = self.root_dir / "build"
        self.dist_dir = self.root_dir / "dist"
        
    def log(self, message, level="INFO"):
        """Log message with timestamp"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def run_command(self, command, cwd=None, shell=True):
        """Run command and return success status"""
        try:
            self.log(f"Running: {command}")
            result = subprocess.run(
                command,
                cwd=cwd or self.root_dir,
                shell=shell,
                check=True,
                capture_output=True,
                text=True
            )
            if result.stdout:
                self.log(f"Output: {result.stdout.strip()}")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"Command failed: {e}", "ERROR")
            if e.stdout:
                self.log(f"Stdout: {e.stdout}", "ERROR")
            if e.stderr:
                self.log(f"Stderr: {e.stderr}", "ERROR")
            return False
    
    def check_prerequisites(self):
        """Check if all required tools are installed"""
        self.log("Checking prerequisites...")
        
        # Check Python
        try:
            result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True)
            self.log(f"Python: {result.stdout.strip()}")
        except Exception as e:
            self.log(f"Python check failed: {e}", "ERROR")
            return False
        
        # Check Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            self.log(f"Node.js: {result.stdout.strip()}")
        except Exception as e:
            self.log("Node.js not found. Please install Node.js 18+", "ERROR")
            return False
        
        # Check npm
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True, shell=True)
            self.log(f"npm: {result.stdout.strip()}")
        except Exception as e:
            self.log(f"npm not found: {e}", "ERROR")
            # Try alternative npm locations
            npm_paths = [
                "npm.cmd",
                r"C:\Program Files\nodejs\npm.cmd",
                r"C:\Program Files (x86)\nodejs\npm.cmd"
            ]

            npm_found = False
            for npm_path in npm_paths:
                try:
                    result = subprocess.run([npm_path, "--version"], capture_output=True, text=True, shell=True)
                    if result.returncode == 0:
                        self.log(f"Found npm at {npm_path}: {result.stdout.strip()}")
                        npm_found = True
                        break
                except Exception:
                    continue

            if not npm_found:
                self.log("npm not found in any common locations", "ERROR")
                self.log("Please ensure Node.js is properly installed with npm", "ERROR")
                self.log("Try running 'npm --version' manually to verify installation", "ERROR")
                return False
        
        return True
    
    def install_backend_dependencies(self):
        """Install Python backend dependencies"""
        self.log("Installing backend dependencies...")
        
        # Create virtual environment if it doesn't exist
        venv_dir = self.backend_dir / "venv"
        if not venv_dir.exists():
            if not self.run_command(f"{sys.executable} -m venv venv", cwd=self.backend_dir):
                return False
        
        # Install dependencies
        pip_cmd = str(venv_dir / "Scripts" / "pip.exe") if os.name == 'nt' else str(venv_dir / "bin" / "pip")
        return self.run_command(f"{pip_cmd} install -r requirements.txt", cwd=self.backend_dir)
    
    def get_npm_command(self):
        """Get the correct npm command for the system"""
        npm_commands = ["npm", "npm.cmd"]

        for cmd in npm_commands:
            try:
                result = subprocess.run([cmd, "--version"], capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    return cmd
            except Exception:
                continue

        return "npm"  # Fallback

    def install_frontend_dependencies(self):
        """Install React frontend dependencies"""
        self.log("Installing frontend dependencies...")
        npm_cmd = self.get_npm_command()
        return self.run_command(f"{npm_cmd} install", cwd=self.frontend_dir)

    def install_electron_dependencies(self):
        """Install Electron dependencies"""
        self.log("Installing Electron dependencies...")
        npm_cmd = self.get_npm_command()
        return self.run_command(f"{npm_cmd} install", cwd=self.electron_dir)
    
    def build_frontend(self):
        """Build React frontend for production"""
        self.log("Building React frontend...")
        npm_cmd = self.get_npm_command()
        return self.run_command(f"{npm_cmd} run build", cwd=self.frontend_dir)
    
    def test_backend(self):
        """Test backend functionality"""
        self.log("Testing backend...")
        # Add backend tests here if available
        return True
    
    def test_frontend(self):
        """Test frontend functionality"""
        self.log("Testing frontend...")
        # Skip tests for now, but could add: npm test -- --watchAll=false
        return True
    
    def package_electron(self):
        """Package Electron application"""
        self.log("Packaging Electron application...")

        # Clean dist directory
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)

        # Build for Windows
        npm_cmd = self.get_npm_command()
        return self.run_command(f"{npm_cmd} run build-win", cwd=self.electron_dir)
    
    def create_installer_info(self):
        """Create installer information file"""
        self.log("Creating installer information...")
        
        info = {
            "name": "Steam Tools v3",
            "version": "3.0.0",
            "description": "Modern Steam activation tool with React frontend and Python backend",
            "build_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "architecture": "x64",
            "platform": "win32",
            "features": [
                "Modern React UI with Steam-inspired design",
                "Python FastAPI backend",
                "Real-time progress updates via WebSocket",
                "KeyAuth license validation",
                "Steam path auto-detection",
                "File download and installation",
                "Admin privilege management",
                "Configuration management"
            ]
        }
        
        info_file = self.dist_dir / "build_info.json"
        with open(info_file, 'w') as f:
            json.dump(info, f, indent=2)
        
        return True
    
    def cleanup(self):
        """Clean up temporary files"""
        self.log("Cleaning up...")
        
        # Clean up node_modules in build artifacts (optional)
        # This could be added if needed to reduce size
        
        return True
    
    def build_all(self):
        """Build the complete application"""
        self.log("Starting Steam Tools v3 build process...")
        
        steps = [
            ("Check prerequisites", self.check_prerequisites),
            ("Install backend dependencies", self.install_backend_dependencies),
            ("Install frontend dependencies", self.install_frontend_dependencies),
            ("Install Electron dependencies", self.install_electron_dependencies),
            ("Build frontend", self.build_frontend),
            ("Test backend", self.test_backend),
            ("Test frontend", self.test_frontend),
            ("Package Electron app", self.package_electron),
            ("Create installer info", self.create_installer_info),
            ("Cleanup", self.cleanup)
        ]
        
        for step_name, step_func in steps:
            self.log(f"Step: {step_name}")
            if not step_func():
                self.log(f"Build failed at step: {step_name}", "ERROR")
                self.log("", "ERROR")
                self.log("TROUBLESHOOTING TIPS:", "ERROR")
                if "prerequisites" in step_name.lower():
                    self.log("1. Run 'check-env.bat' to diagnose environment issues", "ERROR")
                    self.log("2. See MANUAL_SETUP.md for detailed setup instructions", "ERROR")
                    self.log("3. Most common issue: npm not found - reinstall Node.js", "ERROR")
                elif "dependencies" in step_name.lower():
                    self.log("1. Check internet connection", "ERROR")
                    self.log("2. Try running the install commands manually", "ERROR")
                    self.log("3. Clear npm cache: npm cache clean --force", "ERROR")
                else:
                    self.log("1. Check the error messages above for specific issues", "ERROR")
                    self.log("2. Try the manual setup steps in MANUAL_SETUP.md", "ERROR")
                self.log("", "ERROR")
                return False
            self.log(f"Step completed: {step_name}")
        
        self.log("Build completed successfully!")
        self.log(f"Output directory: {self.dist_dir}")
        
        # List output files
        if self.dist_dir.exists():
            self.log("Generated files:")
            for file in self.dist_dir.rglob("*"):
                if file.is_file():
                    size = file.stat().st_size / (1024 * 1024)  # MB
                    self.log(f"  {file.name} ({size:.1f} MB)")
        
        return True

def main():
    """Main entry point"""
    builder = SteamToolsBuilder()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "clean":
            builder.log("Cleaning build artifacts...")
            if builder.dist_dir.exists():
                shutil.rmtree(builder.dist_dir)
            builder.log("Clean completed")
            return
        
        elif command == "frontend-only":
            builder.log("Building frontend only...")
            if (builder.install_frontend_dependencies() and 
                builder.build_frontend()):
                builder.log("Frontend build completed")
            else:
                builder.log("Frontend build failed", "ERROR")
            return
        
        elif command == "backend-only":
            builder.log("Setting up backend only...")
            if builder.install_backend_dependencies():
                builder.log("Backend setup completed")
            else:
                builder.log("Backend setup failed", "ERROR")
            return
    
    # Full build
    success = builder.build_all()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
