@echo off
REM Steam Tools v3 - Environment Check Script

echo ========================================
echo Steam Tools v3 - Environment Check
echo ========================================
echo.

echo Checking system environment...
echo.

REM Check Python
echo [1/4] Checking Python...
python --version 2>nul
if errorlevel 1 (
    echo   ❌ Python not found
    echo   Please install Python 3.8+ from https://python.org/downloads/
) else (
    echo   ✅ Python found
)
echo.

REM Check Node.js
echo [2/4] Checking Node.js...
node --version 2>nul
if errorlevel 1 (
    echo   ❌ Node.js not found
    echo   Please install Node.js 18+ from https://nodejs.org/
) else (
    echo   ✅ Node.js found
)
echo.

REM Check npm (multiple ways)
echo [3/4] Checking npm...
npm --version 2>nul
if errorlevel 1 (
    echo   ❌ npm command not found, trying alternatives...
    
    REM Try npm.cmd
    npm.cmd --version 2>nul
    if errorlevel 1 (
        echo   ❌ npm.cmd not found either
        
        REM Check if npm exists in Node.js installation directory
        if exist "C:\Program Files\nodejs\npm.cmd" (
            echo   ✅ Found npm at C:\Program Files\nodejs\npm.cmd
            "C:\Program Files\nodejs\npm.cmd" --version
        ) else if exist "C:\Program Files (x86)\nodejs\npm.cmd" (
            echo   ✅ Found npm at C:\Program Files (x86)\nodejs\npm.cmd
            "C:\Program Files (x86)\nodejs\npm.cmd" --version
        ) else (
            echo   ❌ npm not found in standard locations
            echo   This usually means Node.js was not installed properly
            echo.
            echo   SOLUTIONS:
            echo   1. Reinstall Node.js from https://nodejs.org/
            echo   2. Make sure to check "Add to PATH" during installation
            echo   3. Restart your command prompt after installation
            echo   4. Try running: npm --version
        )
    ) else (
        echo   ✅ npm.cmd found
    )
) else (
    echo   ✅ npm found
)
echo.

REM Check PATH
echo [4/4] Checking PATH environment...
echo Current PATH contains:
echo %PATH% | findstr /i node
if errorlevel 1 (
    echo   ❌ Node.js not found in PATH
    echo   This is likely the cause of the npm issue
) else (
    echo   ✅ Node.js found in PATH
)
echo.

REM Summary
echo ========================================
echo SUMMARY
echo ========================================
echo.
echo If you see any ❌ above, please fix those issues first.
echo.
echo Common fixes:
echo 1. Reinstall Node.js with "Add to PATH" option checked
echo 2. Restart your command prompt/PowerShell
echo 3. Try running commands manually to verify they work
echo.
echo Once all checks show ✅, try running build.bat again.
echo.

pause
