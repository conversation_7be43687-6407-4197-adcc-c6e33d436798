@echo off
REM Steam Tools v3 - Development Mode Script

echo ========================================
echo Steam Tools v3 - Development Mode
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    pause
    exit /b 1
)

echo Starting development environment...
echo.
echo This will start:
echo 1. Python FastAPI backend (port 8000)
echo 2. React development server (port 3000)
echo 3. Electron application
echo.
echo Press Ctrl+C to stop all processes
echo.

REM Start backend in background
echo Starting Python backend...
cd /d "%~dp0\..\backend"
start "Python Backend" cmd /k "python main.py"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend in background
echo Starting React frontend...
cd /d "%~dp0\..\frontend"
start "React Frontend" cmd /k "npm start"

REM Wait for frontend to be ready
echo Waiting for frontend to be ready...
timeout /t 10 /nobreak >nul

REM Start Electron
echo Starting Electron application...
cd /d "%~dp0\..\electron"
npm run dev

echo.
echo Development session ended.
pause
