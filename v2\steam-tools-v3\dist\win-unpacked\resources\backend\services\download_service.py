"""Download service for handling file downloads and installation."""

import os
import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional
from pathlib import Path

from models.steam import AppInfo, DownloadProgress, DownloadFileInfo
from services.steam_service import SteamService

logger = logging.getLogger(__name__)


class DownloadService:
    """Service for downloading and installing Steam files."""
    
    def __init__(self, steam_service: SteamService, connection_manager):
        self.steam_service = steam_service
        self.connection_manager = connection_manager
        self.current_download: Optional[Dict] = None
    
    async def download_files(self, license_key: str, app_info: AppInfo, steam_path: str) -> bool:
        """Download and install all required files."""
        try:
            logger.info(f"Starting download for app: {app_info.app_name}")
            
            # Prepare download list
            files_to_download = self._prepare_download_list(app_info, steam_path)
            
            if not files_to_download:
                await self._send_progress_update("error", "No files to download", 0, 0)
                return False
            
            # Create necessary directories
            await self._create_directories(steam_path)
            
            # Download each file
            total_files = len(files_to_download)
            
            for i, file_info in enumerate(files_to_download):
                try:
                    await self._send_progress_update(
                        "downloading",
                        f"Downloading {file_info.name}...",
                        i,
                        total_files
                    )
                    
                    success = await self._download_file(file_info)
                    
                    if not success:
                        await self._send_progress_update(
                            "error",
                            f"Failed to download {file_info.name}",
                            i,
                            total_files
                        )
                        return False
                    
                    # Update progress
                    progress = ((i + 1) / total_files) * 100
                    await self._send_progress_update(
                        "downloading",
                        f"Downloaded {file_info.name}",
                        i + 1,
                        total_files,
                        progress
                    )
                    
                except Exception as e:
                    logger.error(f"Error downloading {file_info.name}: {e}")
                    await self._send_progress_update(
                        "error",
                        f"Error downloading {file_info.name}: {str(e)}",
                        i,
                        total_files
                    )
                    return False
            
            # Complete
            await self._send_progress_update(
                "completed",
                "All files downloaded successfully!",
                total_files,
                total_files,
                100
            )
            
            logger.info("Download completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error in download process: {e}")
            await self._send_progress_update("error", f"Download failed: {str(e)}", 0, 0)
            return False
    
    def _prepare_download_list(self, app_info: AppInfo, steam_path: str) -> List[DownloadFileInfo]:
        """Prepare list of files to download."""
        files = []
        
        if not app_info.download_urls:
            return files
        
        # Steam directories
        steam_root = steam_path
        stplug_in_path = os.path.join(steam_path, "config", "stplug-in")
        
        # File mappings based on original implementation
        file_mappings = {
            "hid_dll": {
                "path": os.path.join(steam_root, "hid.dll"),
                "name": "HID Library"
            },
            "luapacka": {
                "path": os.path.join(stplug_in_path, "luapacka.exe"),
                "name": "Lua Packager"
            },
            "steamtools": {
                "path": os.path.join(stplug_in_path, "Steamtools.st"),
                "name": "Steam Tools"
            },
            "lua_script": {
                "path": os.path.join(stplug_in_path, f"{app_info.app_id}.lua"),
                "name": f"Lua Script ({app_info.app_id})"
            }
        }
        
        # Add files that have URLs
        for key, url in app_info.download_urls.items():
            if key in file_mappings and url:
                mapping = file_mappings[key]
                files.append(DownloadFileInfo(
                    url=url,
                    path=mapping["path"],
                    name=mapping["name"]
                ))
        
        return files
    
    async def _create_directories(self, steam_path: str) -> None:
        """Create necessary directories for file installation."""
        directories = [
            os.path.join(steam_path, "config"),
            os.path.join(steam_path, "config", "stplug-in")
        ]
        
        for directory in directories:
            try:
                Path(directory).mkdir(parents=True, exist_ok=True)
                logger.info(f"Created/verified directory: {directory}")
            except Exception as e:
                logger.error(f"Error creating directory {directory}: {e}")
                raise
    
    async def _download_file(self, file_info: DownloadFileInfo) -> bool:
        """Download a single file."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(file_info.url, timeout=30) as response:
                    if response.status == 200:
                        content = await response.read()
                        
                        # Write file
                        with open(file_info.path, 'wb') as f:
                            f.write(content)
                        
                        logger.info(f"Downloaded {file_info.name} to {file_info.path}")
                        return True
                    else:
                        logger.error(f"HTTP {response.status} for {file_info.url}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error downloading {file_info.name}: {e}")
            return False
    
    async def _send_progress_update(self, status: str, message: str, current: int, total: int, percentage: float = 0):
        """Send progress update via WebSocket."""
        try:
            progress_data = {
                "type": "download_progress",
                "status": status,
                "message": message,
                "current_file": current,
                "total_files": total,
                "percentage": percentage,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            await self.connection_manager.broadcast(progress_data)
            
        except Exception as e:
            logger.error(f"Error sending progress update: {e}")
    
    def get_current_download_status(self) -> Optional[Dict]:
        """Get current download status."""
        return self.current_download
