"""Steam service for path detection, process management, and file operations."""

import os
import platform
import subprocess
import winreg
from pathlib import Path
from typing import Optional, List, Tuple
import psutil
import logging

from models.steam import SteamPathResponse, SystemInfo
from config.settings import settings

logger = logging.getLogger(__name__)


class SteamService:
    """Service for Steam-related operations."""
    
    def __init__(self):
        self.steam_path: Optional[str] = None
    
    async def detect_steam_path(self, custom_path: Optional[str] = None) -> SteamPathResponse:
        """Detect Steam installation path."""
        try:
            if custom_path:
                # Validate custom path
                if self._validate_steam_path(custom_path):
                    self.steam_path = custom_path
                    return SteamPathResponse(
                        steam_path=custom_path,
                        is_valid=True
                    )
                else:
                    return SteamPathResponse(
                        steam_path=None,
                        is_valid=False,
                        error_message="Invalid Steam path provided"
                    )
            
            # Auto-detect Steam path
            detected_path = self._auto_detect_steam_path()
            
            if detected_path and self._validate_steam_path(detected_path):
                self.steam_path = detected_path
                return SteamPathResponse(
                    steam_path=detected_path,
                    is_valid=True
                )
            else:
                return SteamPathResponse(
                    steam_path=None,
                    is_valid=False,
                    error_message="Steam installation not found"
                )
                
        except Exception as e:
            logger.error(f"Error detecting Steam path: {str(e)}")
            return SteamPathResponse(
                steam_path=None,
                is_valid=False,
                error_message=f"Error detecting Steam path: {str(e)}"
            )
    
    def _auto_detect_steam_path(self) -> Optional[str]:
        """Auto-detect Steam path from Windows registry."""
        try:
            if platform.system() != "Windows":
                return None
            
            logger.info("Attempting Steam path detection from registry")
            
            # Try to get Steam path from registry
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, settings.steam_registry_path) as key:
                steam_path = winreg.QueryValueEx(key, settings.steam_registry_key)[0]
                
                logger.info(f"Found Steam path in registry: {steam_path}")
                return steam_path
                
        except Exception as e:
            logger.warning(f"Failed to detect Steam path from registry: {str(e)}")
            
            # Try common Steam installation paths
            common_paths = [
                r"C:\Program Files (x86)\Steam",
                r"C:\Program Files\Steam",
                r"D:\Steam",
                r"E:\Steam"
            ]
            
            for path in common_paths:
                if self._validate_steam_path(path):
                    logger.info(f"Found Steam at common path: {path}")
                    return path
            
            return None
    
    def _validate_steam_path(self, path: str) -> bool:
        """Validate if the given path is a valid Steam installation."""
        try:
            steam_exe = os.path.join(path, "steam.exe")
            return os.path.exists(steam_exe)
        except Exception:
            return False
    
    async def get_system_info(self) -> SystemInfo:
        """Get system information including admin status and Steam status."""
        try:
            is_admin = self._is_admin()
            steam_running = self._is_steam_running()
            platform_info = platform.system()
            python_version = platform.python_version()
            
            return SystemInfo(
                is_admin=is_admin,
                steam_running=steam_running,
                steam_path=self.steam_path,
                platform=platform_info,
                python_version=python_version
            )
            
        except Exception as e:
            logger.error(f"Error getting system info: {str(e)}")
            return SystemInfo(
                is_admin=False,
                steam_running=False,
                steam_path=None,
                platform="Unknown",
                python_version="Unknown"
            )
    
    def _is_admin(self) -> bool:
        """Check if the current process has administrator privileges."""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except Exception:
            return False
    
    def _is_steam_running(self) -> bool:
        """Check if Steam is currently running."""
        try:
            steam_processes = ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']
            
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and proc.info['name'].lower() in steam_processes:
                    return True
            return False
        except Exception:
            return False
    
    async def close_steam_processes(self) -> Tuple[bool, str]:
        """Close all Steam-related processes."""
        try:
            steam_processes = []
            steam_process_names = ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']
            
            # Find all Steam processes
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and proc.info['name'].lower() in steam_process_names:
                    steam_processes.append(proc)
            
            if not steam_processes:
                return True, "No Steam processes found"
            
            # Terminate processes gracefully first
            for proc in steam_processes:
                try:
                    proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Wait for graceful termination
            import time
            time.sleep(2)
            
            # Force kill any remaining processes
            for proc in steam_processes:
                try:
                    if proc.is_running():
                        proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            logger.info(f"Closed {len(steam_processes)} Steam process(es)")
            return True, f"Closed {len(steam_processes)} Steam process(es)"
            
        except Exception as e:
            logger.error(f"Error closing Steam processes: {str(e)}")
            return False, f"Error closing Steam processes: {str(e)}"
    
    def get_steam_directories(self) -> dict:
        """Get important Steam directories for file installation."""
        if not self.steam_path:
            return {}
        
        return {
            "steam_root": self.steam_path,
            "config": os.path.join(self.steam_path, "config"),
            "stplug_in": os.path.join(self.steam_path, "config", "stplug-in")
        }
