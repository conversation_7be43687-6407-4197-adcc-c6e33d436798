{"version": 3, "file": "DataSplitter.js", "sourceRoot": "", "sources": ["../../src/differentialDownloader/DataSplitter.ts"], "names": [], "mappings": ";;;AAoBA,4BAaC;AAjCD,+DAA+C;AAC/C,2BAAqC;AACrC,mCAAiC;AACjC,+DAAgE;AAEhE,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;AAE3C,IAAK,SAIJ;AAJD,WAAK,SAAS;IACZ,yCAAI,CAAA;IACJ,6CAAM,CAAA;IACN,yCAAI,CAAA;AACN,CAAC,EAJI,SAAS,KAAT,SAAS,QAIb;AASD,SAAgB,QAAQ,CAAC,IAAe,EAAE,GAAa,EAAE,SAAiB,EAAE,MAA8B,EAAE,OAAmB;IAC7H,MAAM,UAAU,GAAG,IAAA,qBAAgB,EAAC,EAAE,EAAE;QACtC,EAAE,EAAE,SAAS;QACb,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,mBAAmB;QACnB,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;KAClB,CAAC,CAAA;IACF,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;IAC9B,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;IAC/B,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE;QACnB,GAAG,EAAE,KAAK;KACX,CAAC,CAAA;AACJ,CAAC;AAED,MAAa,YAAa,SAAQ,iBAAQ;IAUxC,YACmB,GAAa,EACb,OAAyB,EACzB,oBAAyC,EAC1D,QAAgB,EACC,iBAAgC,EAChC,aAAwB;QAEzC,KAAK,EAAE,CAAA;QAPU,QAAG,GAAH,GAAG,CAAU;QACb,YAAO,GAAP,OAAO,CAAkB;QACzB,yBAAoB,GAApB,oBAAoB,CAAqB;QAEzC,sBAAiB,GAAjB,iBAAiB,CAAe;QAChC,kBAAa,GAAb,aAAa,CAAW;QAf3C,cAAS,GAAG,CAAC,CAAC,CAAA;QAEN,qBAAgB,GAAkB,IAAI,CAAA;QACtC,cAAS,GAAG,SAAS,CAAC,IAAI,CAAA;QAC1B,oBAAe,GAAG,CAAC,CAAA;QACnB,2BAAsB,GAAG,CAAC,CAAA;QA+J1B,qBAAgB,GAAG,CAAC,CAAA;QAjJ1B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAA,CAAC,oBAAoB;QAC9D,sCAAsC;QACtC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA;IAChD,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAA;IACzD,CAAC;IAED,qCAAqC;IACrC,MAAM,CAAC,IAAY,EAAE,QAAgB,EAAE,QAAiC;QACtE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,0BAA0B,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAA;YAC5D,OAAM;QACR,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IACtD,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,KAAa;QACpC,IAAI,KAAK,GAAG,CAAC,CAAA;QAEb,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE,CAAC;YACpE,MAAM,IAAA,+BAAQ,EAAC,gBAAgB,EAAE,uCAAuC,CAAC,CAAA;QAC3E,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;YAC7D,IAAI,CAAC,eAAe,IAAI,QAAQ,CAAA;YAChC,KAAK,GAAG,QAAQ,CAAA;QAClB,CAAC;aAAM,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;YAClE,IAAI,CAAC,sBAAsB,IAAI,MAAM,CAAA;YACrC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,CAAA;YAC5C,KAAK,GAAG,MAAM,CAAA;QAChB,CAAC;QAED,IAAI,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACxC,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC5D,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;gBACzB,OAAM;YACR,CAAC;YAED,KAAK,GAAG,aAAa,CAAA;YACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,IAAI,CAAA;YAC/B,2CAA2C;YAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;QAC9B,CAAC;QAED,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACtC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,IAAI,CAAA;YACjC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,SAAS,EAAE,CAAA;gBAEhB,IAAI,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;gBAC7D,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;oBACtB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACpB,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAA;oBAC9B,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAA,+BAAQ,EAAC,mBAAmB,EAAE,sCAAsC,CAAC,CAAA;oBAC7E,CAAC;gBACH,CAAC;gBAED,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAE,GAAG,CAAC,CAAA,CAAC,4CAA4C;gBACrK,IAAI,aAAa,GAAG,SAAS,EAAE,CAAC;oBAC9B,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;gBACvD,CAAC;qBAAM,IAAI,aAAa,GAAG,SAAS,EAAE,CAAC;oBACrC,MAAM,IAAA,+BAAQ,EAAC,mCAAmC,EAAE,4CAA4C,CAAC,CAAA;gBACnG,CAAC;gBAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBACpB,IAAI,CAAC,SAAS,EAAE,CAAA;oBAChB,IAAI,CAAC,aAAa,EAAE,CAAA;oBACpB,OAAM;gBACR,CAAC;gBAED,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;gBAE9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAA;oBACjC,OAAM;gBACR,CAAC;YACH,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACzD,MAAM,GAAG,GAAG,KAAK,GAAG,UAAU,CAAA;YAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;YAChD,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC,CAAA;YACzD,IAAI,CAAC,sBAAsB,GAAG,UAAU,GAAG,CAAC,YAAY,GAAG,KAAK,CAAC,CAAA;YACjE,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;gBACpC,OAAM;YACR,CAAC;YAED,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAA;YACjC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,CAAA;gBACjE,OAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,KAAa,EAAE,GAAW;QACjD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,CAAC,GAAG,GAAS,EAAE;gBACnB,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;oBAClB,OAAO,EAAE,CAAA;oBACT,OAAM;gBACR,CAAC;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;gBACtC,IAAI,IAAI,CAAC,IAAI,KAAK,mCAAa,CAAC,IAAI,EAAE,CAAC;oBACrC,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAA;oBAC3C,OAAM;gBACR,CAAC;gBAED,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,GAAG,EAAE;oBAC5D,KAAK,EAAE,CAAA;oBACP,CAAC,EAAE,CAAA;gBACL,CAAC,CAAC,CAAA;YACJ,CAAC,CAAA;YACD,CAAC,EAAE,CAAA;QACL,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,mBAAmB,CAAC,KAAa,EAAE,UAAkB;QAC3D,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QAC5D,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;YACzB,OAAO,aAAa,GAAG,WAAW,CAAC,MAAM,CAAA;QAC3C,CAAC;QAED,qDAAqD;QACrD,MAAM,YAAY,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;QACvE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAA;QACtC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAA;QAC9E,CAAC;QACD,OAAO,CAAC,CAAC,CAAA;IACX,CAAC;IAIO,SAAS;QACf,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;QACjE,IAAI,IAAI,CAAC,gBAAgB,KAAK,cAAc,EAAE,CAAC;YAC7C,MAAM,IAAA,+BAAQ,EAAC,oBAAoB,cAAc,yBAAyB,IAAI,CAAC,gBAAgB,EAAE,EAAE,mCAAmC,CAAC,CAAA;QACzI,CAAC;QACD,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAA;IAC3B,CAAC;IAEO,kBAAkB,CAAC,IAAY,EAAE,KAAa,EAAE,GAAW;QACjE,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,EAAE,CAAA;QAClB,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAA;IAC/C,CAAC;IAEO,eAAe,CAAC,IAAY,EAAE,KAAa,EAAE,GAAW;QAC9D,IAAI,CAAC,gBAAgB,IAAI,GAAG,GAAG,KAAK,CAAA;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACpB,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;YAClF,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC1B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;gBACvB,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;oBACrB,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;oBACnC,OAAO,EAAE,CAAA;gBACX,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;CACF;AArMD,oCAqMC", "sourcesContent": ["import { newError } from \"builder-util-runtime\"\nimport { createReadStream } from \"fs\"\nimport { Writable } from \"stream\"\nimport { Operation, OperationKind } from \"./downloadPlanBuilder\"\n\nconst DOUBLE_CRLF = Buffer.from(\"\\r\\n\\r\\n\")\n\nenum ReadState {\n  INIT,\n  HEADER,\n  BODY,\n}\n\nexport interface PartListDataTask {\n  readonly oldFileFd: number\n  readonly tasks: Array<Operation>\n  readonly start: number\n  readonly end: number\n}\n\nexport function copyData(task: Operation, out: Writable, oldFileFd: number, reject: (error: Error) => void, resolve: () => void): void {\n  const readStream = createReadStream(\"\", {\n    fd: oldFileFd,\n    autoClose: false,\n    start: task.start,\n    // end is inclusive\n    end: task.end - 1,\n  })\n  readStream.on(\"error\", reject)\n  readStream.once(\"end\", resolve)\n  readStream.pipe(out, {\n    end: false,\n  })\n}\n\nexport class DataSplitter extends Writable {\n  partIndex = -1\n\n  private headerListBuffer: Buffer | null = null\n  private readState = ReadState.INIT\n  private ignoreByteCount = 0\n  private remainingPartDataCount = 0\n\n  private readonly boundaryLength: number\n\n  constructor(\n    private readonly out: Writable,\n    private readonly options: PartListDataTask,\n    private readonly partIndexToTaskIndex: Map<number, number>,\n    boundary: string,\n    private readonly partIndexToLength: Array<number>,\n    private readonly finishHandler: () => any\n  ) {\n    super()\n\n    this.boundaryLength = boundary.length + 4 /* size of \\r\\n-- */\n    // first chunk doesn't start with \\r\\n\n    this.ignoreByteCount = this.boundaryLength - 2\n  }\n\n  get isFinished(): boolean {\n    return this.partIndex === this.partIndexToLength.length\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  _write(data: Buffer, encoding: string, callback: (error?: Error) => void): void {\n    if (this.isFinished) {\n      console.error(`Trailing ignored data: ${data.length} bytes`)\n      return\n    }\n\n    this.handleData(data).then(callback).catch(callback)\n  }\n\n  private async handleData(chunk: Buffer): Promise<undefined> {\n    let start = 0\n\n    if (this.ignoreByteCount !== 0 && this.remainingPartDataCount !== 0) {\n      throw newError(\"Internal error\", \"ERR_DATA_SPLITTER_BYTE_COUNT_MISMATCH\")\n    }\n\n    if (this.ignoreByteCount > 0) {\n      const toIgnore = Math.min(this.ignoreByteCount, chunk.length)\n      this.ignoreByteCount -= toIgnore\n      start = toIgnore\n    } else if (this.remainingPartDataCount > 0) {\n      const toRead = Math.min(this.remainingPartDataCount, chunk.length)\n      this.remainingPartDataCount -= toRead\n      await this.processPartData(chunk, 0, toRead)\n      start = toRead\n    }\n\n    if (start === chunk.length) {\n      return\n    }\n\n    if (this.readState === ReadState.HEADER) {\n      const headerListEnd = this.searchHeaderListEnd(chunk, start)\n      if (headerListEnd === -1) {\n        return\n      }\n\n      start = headerListEnd\n      this.readState = ReadState.BODY\n      // header list is ignored, we don't need it\n      this.headerListBuffer = null\n    }\n\n    while (true) {\n      if (this.readState === ReadState.BODY) {\n        this.readState = ReadState.INIT\n      } else {\n        this.partIndex++\n\n        let taskIndex = this.partIndexToTaskIndex.get(this.partIndex)\n        if (taskIndex == null) {\n          if (this.isFinished) {\n            taskIndex = this.options.end\n          } else {\n            throw newError(\"taskIndex is null\", \"ERR_DATA_SPLITTER_TASK_INDEX_IS_NULL\")\n          }\n        }\n\n        const prevTaskIndex = this.partIndex === 0 ? this.options.start : this.partIndexToTaskIndex.get(this.partIndex - 1)! + 1 /* prev part is download, next maybe copy */\n        if (prevTaskIndex < taskIndex) {\n          await this.copyExistingData(prevTaskIndex, taskIndex)\n        } else if (prevTaskIndex > taskIndex) {\n          throw newError(\"prevTaskIndex must be < taskIndex\", \"ERR_DATA_SPLITTER_TASK_INDEX_ASSERT_FAILED\")\n        }\n\n        if (this.isFinished) {\n          this.onPartEnd()\n          this.finishHandler()\n          return\n        }\n\n        start = this.searchHeaderListEnd(chunk, start)\n\n        if (start === -1) {\n          this.readState = ReadState.HEADER\n          return\n        }\n      }\n\n      const partLength = this.partIndexToLength[this.partIndex]\n      const end = start + partLength\n      const effectiveEnd = Math.min(end, chunk.length)\n      await this.processPartStarted(chunk, start, effectiveEnd)\n      this.remainingPartDataCount = partLength - (effectiveEnd - start)\n      if (this.remainingPartDataCount > 0) {\n        return\n      }\n\n      start = end + this.boundaryLength\n      if (start >= chunk.length) {\n        this.ignoreByteCount = this.boundaryLength - (chunk.length - end)\n        return\n      }\n    }\n  }\n\n  private copyExistingData(index: number, end: number): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const w = (): void => {\n        if (index === end) {\n          resolve()\n          return\n        }\n\n        const task = this.options.tasks[index]\n        if (task.kind !== OperationKind.COPY) {\n          reject(new Error(\"Task kind must be COPY\"))\n          return\n        }\n\n        copyData(task, this.out, this.options.oldFileFd, reject, () => {\n          index++\n          w()\n        })\n      }\n      w()\n    })\n  }\n\n  private searchHeaderListEnd(chunk: Buffer, readOffset: number): number {\n    const headerListEnd = chunk.indexOf(DOUBLE_CRLF, readOffset)\n    if (headerListEnd !== -1) {\n      return headerListEnd + DOUBLE_CRLF.length\n    }\n\n    // not all headers data were received, save to buffer\n    const partialChunk = readOffset === 0 ? chunk : chunk.slice(readOffset)\n    if (this.headerListBuffer == null) {\n      this.headerListBuffer = partialChunk\n    } else {\n      this.headerListBuffer = Buffer.concat([this.headerListBuffer, partialChunk])\n    }\n    return -1\n  }\n\n  private actualPartLength = 0\n\n  private onPartEnd(): void {\n    const expectedLength = this.partIndexToLength[this.partIndex - 1]\n    if (this.actualPartLength !== expectedLength) {\n      throw newError(`Expected length: ${expectedLength} differs from actual: ${this.actualPartLength}`, \"ERR_DATA_SPLITTER_LENGTH_MISMATCH\")\n    }\n    this.actualPartLength = 0\n  }\n\n  private processPartStarted(data: Buffer, start: number, end: number): Promise<void> {\n    if (this.partIndex !== 0) {\n      this.onPartEnd()\n    }\n    return this.processPartData(data, start, end)\n  }\n\n  private processPartData(data: Buffer, start: number, end: number): Promise<void> {\n    this.actualPartLength += end - start\n    const out = this.out\n    if (out.write(start === 0 && data.length === end ? data : data.slice(start, end))) {\n      return Promise.resolve()\n    } else {\n      return new Promise((resolve, reject) => {\n        out.on(\"error\", reject)\n        out.once(\"drain\", () => {\n          out.removeListener(\"error\", reject)\n          resolve()\n        })\n      })\n    }\n  }\n}\n"]}