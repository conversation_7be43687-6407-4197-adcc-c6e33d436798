{"name": "steam-tools-electron", "version": "3.0.0", "description": "Steam Tools v3 - Desktop Application", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "author": "MTYB", "license": "MIT", "devDependencies": {"electron": "^27.1.3", "electron-builder": "^24.6.4"}, "dependencies": {"electron-is-dev": "^2.0.0", "electron-updater": "^6.1.4"}, "build": {"appId": "com.mtyb.steamtools", "productName": "Steam Tools v3", "directories": {"output": "../dist"}, "files": ["main.js", "preload.js", "../frontend/build/**/*", "node_modules/**/*"], "extraResources": [{"from": "../backend", "to": "backend", "filter": ["**/*", "!**/__pycache__/**/*", "!**/*.pyc"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "requireAdministrator"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Steam Tools v3"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}