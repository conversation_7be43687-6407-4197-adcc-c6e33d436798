// Electron preload script - Security bridge between main and renderer processes

const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  
  // System information
  platform: process.platform,
  
  // Environment
  isDev: process.env.NODE_ENV === 'development'
});

// Expose Steam Tools specific APIs
contextBridge.exposeInMainWorld('steamToolsAPI', {
  // System operations
  isAdmin: () => {
    // This would be implemented to check admin privileges
    return process.platform === 'win32' ? true : false; // Placeholder
  },
  
  // File system operations (if needed)
  // These would be implemented as IPC calls to main process
  
  // Logging
  log: (level, message, data) => {
    console.log(`[${level}] ${message}`, data);
  }
});

// Security: Remove Node.js globals from renderer process
delete window.require;
delete window.exports;
delete window.module;
