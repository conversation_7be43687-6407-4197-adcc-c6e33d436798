/**
 * lucide-react v0.290.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const ToyBrick = createLucideIcon("ToyBrick", [
  [
    "rect",
    { width: "18", height: "12", x: "3", y: "8", rx: "1", key: "158fvp" }
  ],
  ["path", { d: "M10 8V5c0-.6-.4-1-1-1H6a1 1 0 0 0-1 1v3", key: "s0042v" }],
  ["path", { d: "M19 8V5c0-.6-.4-1-1-1h-3a1 1 0 0 0-1 1v3", key: "9wmeh2" }]
]);

export { Toy<PERSON>rick as default };
//# sourceMappingURL=toy-brick.js.map
