// Main App component - Steam Tools v3

import React from 'react';

const App: React.FC = () => {
  const {
    systemInfo,
    isLoading,
    licenseKey,
    licenseValid,
    appInfo,
    downloadProgress,
    isDownloading,
    toasts,
    setLicenseKey,
    validateLicense,
    startDownload,
    removeToast
  } = useAppStore();

  const [hwid] = useState(() => {
    // Generate a mock HWID for display (in real app, this would come from backend)
    return 'ABC123-DEF456-GHI789-JKL012';
  });

  // Copy HWID to clipboard
  const copyHWID = async () => {
    try {
      await navigator.clipboard.writeText(hwid);
      useAppStore.getState().addToast({
        type: 'success',
        title: 'Copied',
        message: 'Hardware ID copied to clipboard!'
      });
    } catch (error) {
      console.error('Failed to copy HWID:', error);
    }
  };

  // Get current status for status indicator
  const getStatus = () => {
    if (isDownloading) {
      return {
        status: 'validating' as const,
        message: downloadProgress?.message || 'Processing...'
      };
    }
    
    if (downloadProgress?.status === 'completed') {
      return {
        status: 'success' as const,
        message: 'License activated successfully!'
      };
    }
    
    if (downloadProgress?.status === 'error') {
      return {
        status: 'error' as const,
        message: downloadProgress.message || 'Activation failed'
      };
    }
    
    if (licenseValid && appInfo) {
      return {
        status: 'success' as const,
        message: `Ready to activate ${appInfo.app_name}`
      };
    }
    
    return {
      status: 'ready' as const,
      message: 'Ready'
    };
  };

  const currentStatus = getStatus();
  const progress = downloadProgress?.percentage || 0;
  const canActivate = licenseValid && appInfo && !isDownloading && !isLoading;
  const isActivated = downloadProgress?.status === 'completed';

  return (
    <div className="steam-container min-h-screen flex flex-col items-center justify-center p-8">
      {/* Header */}
      <div className="steam-header text-center mb-12 animate-slide-in-up">
        <h1 className="text-4xl font-bold text-gradient-steam mb-2 glow-steam">
          🎮 STEAM TOOLS
        </h1>
        <p className="text-lg text-steam-muted-foreground">
          License Activation
        </p>
      </div>

      {/* Main Card */}
      <div className="card-steam w-full max-w-md animate-slide-in-up" style={{ animationDelay: '0.2s' }}>
        {/* Logo Area */}
        <div className="flex flex-col items-center justify-center text-center mb-8 p-6 bg-steam-gradient-secondary rounded-lg border border-steam-border">
          <Gamepad2 className="w-16 h-16 text-steam-primary glow-steam mb-4" />
          <div className="text-xl font-semibold text-steam-primary">
            Steam Tools
          </div>
        </div>

        {/* License Input */}
        <div className="mb-6">
          <LicenseInput
            value={licenseKey}
            onChange={setLicenseKey}
            onSubmit={validateLicense}
            disabled={isLoading || isDownloading}
            error={licenseKey.length === 19 && !licenseValid}
            success={licenseValid}
          />
        </div>

        {/* Activate Button */}
        <div className="mb-6">
          <ActivateButton
            onClick={canActivate ? startDownload : validateLicense}
            disabled={isLoading || isDownloading || (!licenseKey.trim())}
            loading={isLoading || isDownloading}
            success={isActivated}
          >
            {canActivate ? 'Activate License' : 'Validate License'}
          </ActivateButton>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <ProgressBar
            percentage={progress}
            status={currentStatus.message}
            animated={isDownloading}
          />
        </div>

        {/* HWID Section */}
        <div className="mb-6 bg-steam-gradient-secondary border border-steam-border rounded-lg p-4">
          <div className="text-sm font-medium text-steam-muted-foreground mb-2">
            Hardware ID
          </div>
          <div 
            className="font-mono text-steam-foreground mb-3 p-3 bg-steam-muted rounded cursor-pointer hover:bg-steam-input transition-colors break-all"
            onClick={copyHWID}
            title="Click to copy"
          >
            {hwid}
          </div>
          <StatusIndicator
            status={currentStatus.status}
            message={currentStatus.message}
          />
        </div>

        {/* Support Buttons */}
        <div className="flex gap-4 justify-center">
          <button className="btn-steam-secondary">
            <HelpCircle className="w-4 h-4" />
            Help
          </button>
          <button className="btn-steam-secondary">
            <MessageCircle className="w-4 h-4" />
            Support
          </button>
        </div>
      </div>

      {/* System Status Bar */}
      <div className="mt-8 flex items-center gap-6 text-sm text-steam-muted-foreground">
        <div className="flex items-center gap-2">
          {systemInfo?.is_admin ? (
            <ShieldCheck className="w-4 h-4 text-steam-accent" />
          ) : (
            <Shield className="w-4 h-4 text-steam-warning" />
          )}
          <span>
            Admin: {systemInfo?.is_admin ? 'Yes' : 'No'}
          </span>
        </div>
        
        <div className="text-steam-border">•</div>
        
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${systemInfo?.steam_path ? 'bg-steam-accent' : 'bg-steam-destructive'}`} />
          <span>
            Steam: {systemInfo?.steam_path ? 'Found' : 'Not Found'}
          </span>
        </div>
      </div>

      {/* Toast Container */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
};

export default App;
