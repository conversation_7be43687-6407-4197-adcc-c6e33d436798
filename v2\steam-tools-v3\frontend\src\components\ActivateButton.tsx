// Activate button component with loading and success states

import React from 'react';
import { Check, Lock } from 'lucide-react';
import { ActivateButtonProps } from '../types';

const ActivateButton: React.FC<ActivateButtonProps> = ({
  onClick,
  disabled = false,
  loading = false,
  success = false,
  children
}) => {
  const handleClick = () => {
    if (!disabled && !loading) {
      onClick();
    }
  };

  const buttonClasses = [
    'w-full flex items-center justify-center gap-2 py-4 px-6 rounded-lg font-semibold text-lg transition-all duration-200 relative overflow-hidden',
    success 
      ? 'bg-steam-gradient-success text-steam-accent-foreground' 
      : 'bg-steam-gradient-primary text-steam-primary-foreground',
    !disabled && !loading && 'hover:scale-105 hover:shadow-steam-glow active:scale-98',
    disabled && 'opacity-50 cursor-not-allowed',
    loading && 'cursor-wait'
  ].filter(Boolean).join(' ');

  return (
    <button
      onClick={handleClick}
      disabled={disabled || loading}
      className={buttonClasses}
    >
      {/* Loading spinner */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-inherit">
          <div className="spinner border-current"></div>
        </div>
      )}
      
      {/* Button content */}
      <div className={`flex items-center justify-center gap-2 ${loading ? 'opacity-0' : 'opacity-100'}`}>
        {success ? (
          <>
            <Check className="w-5 h-5" />
            <span>Activated</span>
          </>
        ) : (
          <>
            <Lock className="w-5 h-5" />
            <span>{children}</span>
          </>
        )}
      </div>
    </button>
  );
};

export default ActivateButton;
