// License input component with validation and formatting

import React, { useState, useEffect } from 'react';
import { LicenseInputProps } from '../types';

const LicenseInput: React.FC<LicenseInputProps> = ({
  value,
  onChange,
  onSubmit,
  disabled = false,
  error = false,
  success = false
}) => {
  const [focused, setFocused] = useState(false);

  // Format license key input (XXXX-XXXX-XXXX-XXXX)
  const formatLicenseKey = (input: string): string => {
    // Remove all non-alphanumeric characters and convert to uppercase
    const cleaned = input.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    
    // Split into groups of 4 characters
    const groups = cleaned.match(/.{1,4}/g) || [];
    
    // Join with dashes and limit to 19 characters (4-4-4-4 + 3 dashes)
    const formatted = groups.join('-');
    return formatted.length > 19 ? formatted.substring(0, 19) : formatted;
  };

  // Validate license key format
  const isValidFormat = (key: string): boolean => {
    return /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(key);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatLicenseKey(e.target.value);
    onChange(formatted);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !disabled) {
      onSubmit();
    }
  };

  const inputClasses = [
    'input-steam',
    error && 'error',
    success && 'success',
    focused && 'ring-2 ring-steam-primary ring-opacity-50'
  ].filter(Boolean).join(' ');

  return (
    <div className="license-section">
      <label className="block text-sm font-medium text-steam-foreground mb-2">
        License Key
      </label>
      <input
        type="text"
        value={value}
        onChange={handleInputChange}
        onKeyPress={handleKeyPress}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        disabled={disabled}
        placeholder="XXXX-XXXX-XXXX-XXXX"
        maxLength={19}
        className={inputClasses}
        autoComplete="off"
        spellCheck={false}
      />
      
      {/* Validation indicator */}
      <div className="mt-2 flex items-center justify-between text-sm">
        <div className="flex items-center space-x-2">
          {value && (
            <>
              {isValidFormat(value) ? (
                <div className="flex items-center text-steam-accent">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Valid format
                </div>
              ) : value.length === 19 ? (
                <div className="flex items-center text-steam-destructive">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  Invalid format
                </div>
              ) : null}
            </>
          )}
        </div>
        
        <div className="text-steam-muted-foreground">
          {value.length}/19
        </div>
      </div>
    </div>
  );
};

export default LicenseInput;
