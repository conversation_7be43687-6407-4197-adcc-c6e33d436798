// Progress bar component with Steam-inspired styling

import React from 'react';
import { ProgressBarProps } from '../types';

const ProgressBar: React.FC<ProgressBarProps> = ({
  percentage,
  status,
  animated = true
}) => {
  return (
    <div className="progress-section">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-steam-foreground">
          Activation Progress
        </span>
        <span className="text-sm text-steam-muted-foreground">
          {Math.round(percentage)}%
        </span>
      </div>
      
      <div className="progress-steam">
        <div 
          className={`progress-steam-fill ${animated ? 'transition-all duration-700 ease-out' : ''}`}
          style={{ width: `${percentage}%` }}
        >
          {/* Shimmer effect */}
          {animated && percentage > 0 && percentage < 100 && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />
          )}
        </div>
      </div>
      
      {/* Status message */}
      {status && (
        <div className="mt-2 text-sm text-steam-muted-foreground">
          {status}
        </div>
      )}
    </div>
  );
};

export default ProgressBar;
