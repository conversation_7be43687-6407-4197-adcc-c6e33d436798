// Status indicator component with animated dot

import React from 'react';
import { StatusIndicatorProps } from '../types';

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  message
}) => {
  const getDotClasses = () => {
    const baseClasses = 'status-dot';
    return `${baseClasses} ${status}`;
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return '✓';
      case 'error':
        return '✗';
      case 'validating':
        return '⟳';
      default:
        return '●';
    }
  };

  return (
    <div className="flex items-center gap-2 text-sm">
      <div className={getDotClasses()} />
      <span className="text-steam-foreground">
        {message}
      </span>
    </div>
  );
};

export default StatusIndicator;
