// Toast notification component

import React, { useEffect, useState } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { ToastProps } from '../types';

const Toast: React.FC<ToastProps> = ({ toast, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Show toast with animation
    const showTimer = setTimeout(() => setIsVisible(true), 100);
    
    return () => clearTimeout(showTimer);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(toast.id), 300);
  };

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-steam-accent" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-steam-destructive" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-steam-warning" />;
      default:
        return <Info className="w-5 h-5 text-steam-primary" />;
    }
  };

  const getToastClasses = () => {
    const baseClasses = 'toast';
    const typeClasses = toast.type;
    const visibilityClasses = isVisible ? 'show' : '';
    
    return `${baseClasses} ${typeClasses} ${visibilityClasses}`;
  };

  return (
    <div className={getToastClasses()}>
      <div className="flex items-start gap-3">
        {getIcon()}
        
        <div className="flex-1 min-w-0">
          <div className="font-medium text-steam-foreground">
            {toast.title}
          </div>
          <div className="text-sm text-steam-muted-foreground mt-1">
            {toast.message}
          </div>
        </div>
        
        <button
          onClick={handleClose}
          className="flex-shrink-0 p-1 rounded-md hover:bg-steam-muted transition-colors"
        >
          <X className="w-4 h-4 text-steam-muted-foreground" />
        </button>
      </div>
    </div>
  );
};

// Toast container component
export const ToastContainer: React.FC<{ toasts: any[], onClose: (id: string) => void }> = ({ 
  toasts, 
  onClose 
}) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map(toast => (
        <Toast key={toast.id} toast={toast} onClose={onClose} />
      ))}
    </div>
  );
};

export default Toast;
