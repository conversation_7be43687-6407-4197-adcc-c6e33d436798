@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #0e1419;
  color: #c7d5e0;
  min-height: 100vh;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #171a21;
}

::-webkit-scrollbar-thumb {
  background: #2a475e;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #66c0f4;
}

/* Steam container base */
.steam-container {
  background: #0e1419;
  min-height: 100vh;
  position: relative;
}

.steam-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(102, 192, 244, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

/* Custom button styles */
.btn-steam {
  @apply bg-steam-gradient-primary text-steam-primary-foreground font-semibold py-3 px-6 rounded-md transition-all duration-200 hover:scale-105 hover:shadow-steam-glow active:scale-98 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
}

.btn-steam-secondary {
  @apply bg-transparent border border-steam-border text-steam-muted-foreground font-medium py-2 px-4 rounded-md transition-all duration-150 hover:border-steam-primary hover:text-steam-primary hover:scale-105;
}

/* Custom input styles */
.input-steam {
  @apply w-full p-4 bg-steam-input border-2 border-steam-border rounded-md text-steam-foreground font-mono text-lg text-center tracking-wider transition-all duration-300 focus:outline-none focus:border-steam-primary focus:shadow-steam-glow focus:scale-102;
}

.input-steam.error {
  @apply border-steam-destructive animate-shake;
}

.input-steam.success {
  @apply border-steam-accent shadow-[0_0_20px_rgba(76,107,34,0.3)];
}

/* Custom card styles */
.card-steam {
  @apply bg-steam-gradient-card border border-steam-border rounded-lg p-8 shadow-steam-card relative;
}

.card-steam::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #66c0f4, transparent);
  opacity: 0.5;
}

/* Progress bar styles */
.progress-steam {
  @apply w-full h-2 bg-steam-muted rounded-full overflow-hidden relative;
}

.progress-steam-fill {
  @apply h-full bg-steam-gradient-primary rounded-full transition-all duration-700 ease-out relative overflow-hidden;
}

.progress-steam-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

/* Status indicator styles */
.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-dot.ready {
  @apply bg-steam-muted-foreground animate-pulse-slow;
}

.status-dot.validating {
  @apply bg-steam-primary animate-pulse-slow;
}

.status-dot.success {
  @apply bg-steam-accent;
}

.status-dot.error {
  @apply bg-steam-destructive;
}

/* Toast styles */
.toast {
  @apply fixed top-8 right-8 bg-steam-card border border-steam-border rounded-lg p-4 text-steam-foreground shadow-lg transform translate-x-full transition-transform duration-300 z-50;
}

.toast.show {
  @apply translate-x-0;
}

.toast.success {
  @apply border-steam-accent bg-gradient-to-r from-steam-accent to-steam-card;
}

.toast.error {
  @apply border-steam-destructive bg-gradient-to-r from-steam-destructive to-steam-card;
}

/* Loading spinner */
.spinner {
  @apply w-5 h-5 border-2 border-transparent border-t-current rounded-full animate-spin;
}

/* Utility classes */
.text-gradient-steam {
  background: linear-gradient(135deg, #66c0f4 0%, #4c9eff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-steam {
  filter: drop-shadow(0 0 20px rgba(102, 192, 244, 0.3));
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .steam-container {
    padding: 1rem;
  }
  
  .card-steam {
    padding: 2rem;
  }
}
