// API service for communicating with the FastAPI backend

import { 
  ApiClient, 
  LicenseValidationResponse, 
  SteamPathResponse, 
  SystemInfo, 
  StatusResponse, 
  AdminResponse, 
  ConfigData,
  AppInfo
} from '../types';

class ApiService implements ApiClient {
  private baseUrl: string;

  constructor() {
    // In development, use proxy. In production, use relative URLs
    this.baseUrl = process.env.NODE_ENV === 'development' ? '' : '';
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}/api${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const response = await fetch(url, { ...defaultOptions, ...options });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  async validateLicense(licenseKey: string): Promise<LicenseValidationResponse> {
    return this.request<LicenseValidationResponse>('/license/validate', {
      method: 'POST',
      body: JSON.stringify({ license_key: licenseKey }),
    });
  }

  async detectSteamPath(customPath?: string): Promise<SteamPathResponse> {
    return this.request<SteamPathResponse>('/steam/detect-path', {
      method: 'POST',
      body: JSON.stringify({ 
        auto_detect: !customPath,
        custom_path: customPath 
      }),
    });
  }

  async getSystemInfo(): Promise<SystemInfo> {
    return this.request<SystemInfo>('/system/info');
  }

  async startDownload(
    licenseKey: string, 
    appInfo: AppInfo, 
    steamPath: string
  ): Promise<StatusResponse> {
    return this.request<StatusResponse>('/download/start', {
      method: 'POST',
      body: JSON.stringify({
        license_key: licenseKey,
        app_info: appInfo,
        steam_path: steamPath,
      }),
    });
  }

  async closeSteamProcesses(): Promise<StatusResponse> {
    return this.request<StatusResponse>('/steam/close-processes', {
      method: 'POST',
    });
  }

  async authenticateAdmin(password: string): Promise<AdminResponse> {
    return this.request<AdminResponse>('/admin/authenticate', {
      method: 'POST',
      body: JSON.stringify({ password }),
    });
  }

  async getConfig(): Promise<{ success: boolean; data: ConfigData }> {
    return this.request<{ success: boolean; data: ConfigData }>('/config');
  }

  async resetConfig(): Promise<StatusResponse> {
    return this.request<StatusResponse>('/config/reset', {
      method: 'POST',
    });
  }

  // Health check
  async healthCheck(): Promise<{ status: string; version: string }> {
    const response = await fetch(`${this.baseUrl}/health`);
    return response.json();
  }
}

// Create singleton instance
export const apiService = new ApiService();
export default apiService;
