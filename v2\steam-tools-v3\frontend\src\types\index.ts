// Type definitions for Steam Tools v3

export interface AppInfo {
  app_id: string;
  app_name: string;
  license_prefix: string;
  download_urls?: Record<string, string>;
}

export interface LicenseValidationResponse {
  is_valid: boolean;
  app_info?: AppInfo;
  error_message?: string;
}

export interface SteamPathResponse {
  steam_path?: string;
  is_valid: boolean;
  error_message?: string;
}

export interface SystemInfo {
  is_admin: boolean;
  steam_running: boolean;
  steam_path?: string;
  platform: string;
  python_version: string;
}

export interface DownloadProgress {
  type: 'download_progress';
  status: 'downloading' | 'completed' | 'error';
  message: string;
  current_file: number;
  total_files: number;
  percentage: number;
  timestamp: number;
}

export interface StatusResponse {
  success: boolean;
  message: string;
  data?: Record<string, any>;
}

export interface LicenseHistoryEntry {
  key: string;
  app_name: string;
  app_id: string;
  license_prefix: string;
  timestamp: string;
  success: boolean;
}

export interface ConfigData {
  steam_path: string;
  license_key_history: LicenseHistoryEntry[];
  admin_settings: Record<string, any>;
  app_cache: Record<string, any>;
  last_used: Record<string, any>;
}

export interface AdminResponse {
  authenticated: boolean;
  error_message?: string;
}

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  duration?: number;
}

export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

// Component prop types
export interface LicenseInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  disabled?: boolean;
  error?: boolean;
  success?: boolean;
}

export interface ProgressBarProps {
  percentage: number;
  status: string;
  animated?: boolean;
}

export interface StatusIndicatorProps {
  status: 'ready' | 'validating' | 'success' | 'error';
  message: string;
}

export interface ActivateButtonProps {
  onClick: () => void;
  disabled?: boolean;
  loading?: boolean;
  success?: boolean;
  children: React.ReactNode;
}

export interface ToastProps {
  toast: ToastMessage;
  onClose: (id: string) => void;
}

// Store types
export interface AppState {
  // System state
  systemInfo: SystemInfo | null;
  isLoading: boolean;
  
  // License state
  licenseKey: string;
  licenseValid: boolean;
  appInfo: AppInfo | null;
  
  // Download state
  downloadProgress: DownloadProgress | null;
  isDownloading: boolean;
  
  // UI state
  toasts: ToastMessage[];
  
  // Actions
  setLicenseKey: (key: string) => void;
  validateLicense: () => Promise<void>;
  startDownload: () => Promise<void>;
  addToast: (toast: Omit<ToastMessage, 'id'>) => void;
  removeToast: (id: string) => void;
  updateSystemInfo: () => Promise<void>;
  setDownloadProgress: (progress: DownloadProgress | null) => void;
}

// API types
export interface ApiClient {
  validateLicense: (licenseKey: string) => Promise<LicenseValidationResponse>;
  detectSteamPath: (customPath?: string) => Promise<SteamPathResponse>;
  getSystemInfo: () => Promise<SystemInfo>;
  startDownload: (licenseKey: string, appInfo: AppInfo, steamPath: string) => Promise<StatusResponse>;
  closeSteamProcesses: () => Promise<StatusResponse>;
  authenticateAdmin: (password: string) => Promise<AdminResponse>;
  getConfig: () => Promise<{ success: boolean; data: ConfigData }>;
  resetConfig: () => Promise<StatusResponse>;
}

// WebSocket types
export interface WebSocketClient {
  connect: (clientId: string) => void;
  disconnect: () => void;
  send: (message: WebSocketMessage) => void;
  onMessage: (callback: (message: WebSocketMessage) => void) => void;
  isConnected: boolean;
}
