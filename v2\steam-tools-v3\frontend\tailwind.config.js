/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  theme: {
    extend: {
      colors: {
        // Steam-inspired color palette
        steam: {
          background: '#0e1419',
          foreground: '#c7d5e0',
          card: '#1b2838',
          'card-foreground': '#c7d5e0',
          popover: '#1b2838',
          'popover-foreground': '#c7d5e0',
          primary: '#66c0f4',
          'primary-foreground': '#0e1419',
          secondary: '#2a475e',
          'secondary-foreground': '#c7d5e0',
          muted: '#171a21',
          'muted-foreground': '#8b98a5',
          accent: '#4c6b22',
          'accent-foreground': '#c7d5e0',
          destructive: '#cd412b',
          'destructive-foreground': '#ffffff',
          border: '#2a475e',
          input: '#1b2838',
          ring: '#66c0f4',
          success: '#4c6b22',
          warning: '#ffa500',
          error: '#cd412b'
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'monospace']
      },
      backgroundImage: {
        'steam-gradient-primary': 'linear-gradient(135deg, #66c0f4 0%, #4c9eff 100%)',
        'steam-gradient-secondary': 'linear-gradient(135deg, #2a475e 0%, #1b2838 100%)',
        'steam-gradient-success': 'linear-gradient(135deg, #4c6b22 0%, #5c7e10 100%)',
        'steam-gradient-card': 'linear-gradient(145deg, #1b2838 0%, #16202d 100%)',
        'radial-glow': 'radial-gradient(circle at 50% 50%, rgba(102, 192, 244, 0.05) 0%, transparent 70%)'
      },
      boxShadow: {
        'steam-glow': '0 0 20px rgba(102, 192, 244, 0.3)',
        'steam-glow-lg': '0 0 30px rgba(102, 192, 244, 0.5)',
        'steam-card': '0 20px 25px 0 rgba(0, 0, 0, 0.5), 0 10px 10px 0 rgba(0, 0, 0, 0.4)'
      },
      animation: {
        'steam-glow': 'steamGlow 2s ease-in-out infinite alternate',
        'slide-in-up': 'slideInUp 0.8s ease-out',
        'progress-fill': 'progressFill 0.8s ease-out',
        'pulse-slow': 'pulse 2s infinite',
        'shake': 'shake 0.4s ease-in-out',
        'shimmer': 'shimmer 2s infinite'
      },
      keyframes: {
        steamGlow: {
          '0%, 100%': { boxShadow: '0 0 20px rgba(102, 192, 244, 0.3)' },
          '50%': { boxShadow: '0 0 30px rgba(102, 192, 244, 0.5)' }
        },
        slideInUp: {
          from: {
            opacity: '0',
            transform: 'translateY(30px) scale(0.95)'
          },
          to: {
            opacity: '1',
            transform: 'translateY(0) scale(1)'
          }
        },
        progressFill: {
          from: { width: '0%' },
          to: { width: 'var(--progress-width, 0%)' }
        },
        shake: {
          '0%, 100%': { transform: 'translateX(0)' },
          '25%': { transform: 'translateX(-5px)' },
          '75%': { transform: 'translateX(5px)' }
        },
        shimmer: {
          '0%': { left: '-100%' },
          '100%': { left: '100%' }
        }
      }
    }
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography')
  ]
}
